{"cells": [{"cell_type": "markdown", "id": "e4a9115b", "metadata": {}, "source": ["# Default dictionary vs standard dictionary"]}, {"cell_type": "markdown", "id": "f2c84a61", "metadata": {}, "source": ["## Using a standard dictionary:"]}, {"cell_type": "code", "execution_count": 10, "id": "46fab40c", "metadata": {}, "outputs": [], "source": ["transactions = {}\n", "transactions['Alice'] = 100\n", "# transactions['Bob'] = \"not money\"  # TypeError: can only concatenate str (not \"int\") to str\n", "transactions['Bob'] = 50\n", "\n", "# To add to <PERSON>'s existing transactions:\n", "if 'Alice' in transactions:\n", "    transactions['Alice'] += 20\n", "else:\n", "    transactions['<PERSON>'] = 20 # This case wouldn't happen here, but imagine if '<PERSON>' wasn't there initially\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e00a8527", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "can only concatenate str (not \"int\") to str", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Or using get()\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m transactions[\u001b[33m'\u001b[39m\u001b[33mBob\u001b[39m\u001b[33m'\u001b[39m] = \u001b[43mtransactions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mBob\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m30\u001b[39;49m\n\u001b[32m      3\u001b[39m transactions[\u001b[33m'\u001b[39m\u001b[33mCharlie\u001b[39m\u001b[33m'\u001b[39m] = transactions.get(\u001b[33m'\u001b[39m\u001b[33mCharlie\u001b[39m\u001b[33m'\u001b[39m, \u001b[32m0\u001b[39m) + \u001b[32m75\u001b[39m \u001b[38;5;66;03m# <PERSON> didn't exist before\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[38;5;28mprint\u001b[39m(transactions) \u001b[38;5;66;03m# Output: {'<PERSON>': 120, '<PERSON>': 80, '<PERSON>': 75}\u001b[39;00m\n", "\u001b[31mTypeError\u001b[39m: can only concatenate str (not \"int\") to str"]}], "source": ["\n", "# Or using get()\n", "transactions['Bob'] = transactions.get('<PERSON>', 0) + 30\n", "transactions['Charlie'] = transactions.get('<PERSON>', 0) + 75 # Charlie didn't exist before\n", "\n", "print(transactions) # Output: {'<PERSON>': 120, '<PERSON>': 80, '<PERSON>': 75}"]}, {"cell_type": "markdown", "id": "2b5078e0", "metadata": {}, "source": ["## using default dict"]}, {"cell_type": "code", "execution_count": 11, "id": "4b82ce11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["defaultdict(<class 'int'>, {'<PERSON>': 120, '<PERSON>': 50, '<PERSON>': 75})\n"]}], "source": ["from collections import defaultdict\n", "\n", "outgoing_transactions = defaultdict(int)\n", "outgoing_transactions['Alice'] += 100\n", "outgoing_transactions['Bob'] += 50\n", "outgoing_transactions['Alice'] += 20  # Alice already exists, so it adds to her current value\n", "outgoing_transactions['Charlie'] += 75 # Charlie doesn't exist, so it initializes to 0 then adds 75\n", "\n", "print(outgoing_transactions) # Output: defaultdict(<class 'int'>, {'<PERSON>': 120, '<PERSON>': 50, '<PERSON>': 75})"]}, {"cell_type": "code", "execution_count": 14, "id": "b0b7cd7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'collections.defaultdict'>\n", "defaultdict(<class 'int'>, {'<PERSON>': 120, '<PERSON>': 50, '<PERSON>': 75})\n"]}], "source": ["print(type(outgoing_transactions))\n", "print(outgoing_transactions)"]}, {"cell_type": "code", "execution_count": 15, "id": "0391ab7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'dict'>\n", "{'<PERSON>': 120, '<PERSON>': 50}\n"]}], "source": ["print(type(transactions))\n", "print(transactions)"]}, {"cell_type": "code", "execution_count": null, "id": "99cd8732", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8b066ad2", "metadata": {}, "source": ["# Advance Sorting"]}, {"cell_type": "code", "execution_count": null, "id": "e590a720", "metadata": {}, "outputs": [], "source": ["students = [\n", "    (\"<PERSON>\", \"A\", 16),\n", "    (\"<PERSON>\", \"B\", 17),\n", "    (\"<PERSON>\", \"A\", 15),\n", "    (\"<PERSON>\", \"<PERSON>\", 16),\n", "    (\"Eve\", \"B\", 15),\n", "    (\"<PERSON>\", \"A\", 17)\n", "]"]}, {"cell_type": "code", "execution_count": 17, "id": "0e6b7135", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('<PERSON>', '<PERSON>', 16), ('<PERSON>', '<PERSON>', 15), ('<PERSON>', '<PERSON>', 17), ('<PERSON>', '<PERSON>', 17), ('<PERSON>', 'B', 15), ('<PERSON>', '<PERSON>', 16)]\n"]}], "source": ["# sort by grade, then name, then age in descending order\n", "students.sort(key = lambda x: (x[1], x[0], -x[2]))\n", "print(students)"]}, {"cell_type": "code", "execution_count": null, "id": "9b3f93a9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}