from banking_system import BankingSystem

class BankingSystemImpl(BankingSystem):
    def __init__(self):
        self.accounts = {}

    def create_account(self, timestamp, account_id):
        if account_id in self.accounts:
            return False
        self.accounts[account_id] = 0
        return True
    
    def deposit(self, timestamp, account_id, amount):
        if (account_id not in self.accounts) or amount <= 0:
            return None
        self.accounts[account_id] += amount
        return self.accounts[account_id] 
    
    def transfer(self, timestamp, source_account_id, target_account_id, amount):
        if source_account_id == target_account_id:
            return None
        if (source_account_id not in self.accounts) or (target_account_id not in self.accounts) or amount <= 0:
            return None
        if self.accounts[source_account_id] < amount:
            return None
        self.accounts[source_account_id] -= amount
        self.accounts[target_account_id] += amount
        return self.accounts[source_account_id]
    
