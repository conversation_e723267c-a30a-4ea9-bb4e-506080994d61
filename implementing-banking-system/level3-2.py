from banking_system import BankingSystem
from collections import defaultdict
import math # For math.floor for rounding cashback
import heapq # For managing scheduled cashbacks efficiently

# Define the constant for 24 hours in milliseconds
MILLISECONDS_IN_1_DAY = 24 * 60 * 60 * 1000 # 86,400,000

class BankingSystemImpl(BankingSystem):
    def __init__(self):
        self.accounts = {}
        self.outgoing_transactions = defaultdict(int) # Using defaultdict for convenience

        # Level 3 additions:
        self.payment_counter = 0 # Global counter for unique payment IDs
        # Stores details for each payment: {payment_id: {account_id, original_timestamp, cashback_amount, refund_timestamp, status}}
        self.payment_details = {} 
        # Min-heap to store scheduled cashbacks: [(refund_timestamp, payment_id), ...]
        # Using a heap allows efficient retrieval of the next due cashback.
        self.scheduled_cashbacks = [] 

    # Helper method to process any due scheduled payments (cashbacks)
    # This must be called at the beginning of *every* public method.
    def _process_scheduled_payments(self, current_timestamp):
        while self.scheduled_cashbacks and \
              self.scheduled_cashbacks[0][0] <= current_timestamp:
            
            # Get the earliest due cashback from the heap
            refund_time, payment_id_to_process = heapq.heappop(self.scheduled_cashbacks)
            
            # Retrieve payment details
            payment_info = self.payment_details.get(payment_id_to_process)
            
            # This check is for robustness, though usually not strictly necessary for CodeSignal as tests are well-behaved
            if payment_info and payment_info['status'] == "IN_PROGRESS":
                account_id = payment_info['account_id']
                cashback_amount = payment_info['cashback_amount']
                
                # Refund the cashback
                # Ensure account still exists (robustness)
                if account_id in self.accounts:
                    self.accounts[account_id] += cashback_amount
                
                # Update the status of the payment
                payment_info['status'] = "CASHBACK_RECEIVED"


    def create_account(self, timestamp, account_id):
        # Always process scheduled payments first
        self._process_scheduled_payments(timestamp)

        if account_id in self.accounts:
            return False
        self.accounts[account_id] = 0
        return True
    
    def deposit(self, timestamp, account_id, amount):
        # Always process scheduled payments first
        self._process_scheduled_payments(timestamp)

        if (account_id not in self.accounts) or amount <= 0:
            return None
        self.accounts[account_id] += amount
        return self.accounts[account_id] 
    
    def transfer(self, timestamp, source_account_id, target_account_id, amount):
        # Always process scheduled payments first
        self._process_scheduled_payments(timestamp)

        if source_account_id == target_account_id:
            return None
        if (source_account_id not in self.accounts) or (target_account_id not in self.accounts) or amount <= 0:
            return None
        if self.accounts[source_account_id] < amount:
            return None
        
        self.accounts[source_account_id] -= amount
        self.accounts[target_account_id] += amount
        
        self.outgoing_transactions[source_account_id] += amount
        
        return self.accounts[source_account_id]

    def top_spenders(self, timestamp, n):
        # Always process scheduled payments first
        self._process_scheduled_payments(timestamp)

        outgoing_totals = [
            (account_id, total) 
            for account_id, total in self.outgoing_transactions.items()
        ]
        outgoing_totals.sort(key=lambda x: (-x[1], x[0]))

        result = [
            f"{account_id}({total})" for account_id, total in outgoing_totals[:n]
        ]

        return result
    
    # Level 3: New method - pay
    def pay(self, timestamp, account_id, amount):
        # Always process scheduled payments first
        self._process_scheduled_payments(timestamp)

        if account_id not in self.accounts:
            return None
        if amount <= 0: # Payments should be positive
            return None
        if self.accounts[account_id] < amount:
            return None
        
        # Withdraw the amount
        self.accounts[account_id] -= amount
        self.outgoing_transactions[account_id] += amount # Payments are also outgoing transactions
        
        # Generate unique payment ID
        self.payment_counter += 1
        payment_id = f"payment{self.payment_counter}"
        
        # Calculate cashback
        cashback_amount = math.floor(0.02 * amount)
        refund_timestamp = timestamp + MILLISECONDS_IN_1_DAY
        
        # Store payment details
        self.payment_details[payment_id] = {
            'account_id': account_id,
            'original_timestamp': timestamp,
            'amount': amount,
            'cashback_amount': cashback_amount,
            'refund_timestamp': refund_timestamp,
            'status': "IN_PROGRESS"
        }
        
        # Schedule the cashback
        # Add to min-heap: (refund_time, payment_id)
        heapq.heappush(self.scheduled_cashbacks, (refund_timestamp, payment_id))
        
        return payment_id

    # Level 3: New method - get_payment_status
    def get_payment_status(self, timestamp, account_id, payment_id):
        # Always process scheduled payments first
        self._process_scheduled_payments(timestamp)

        if account_id not in self.accounts:
            return None
        
        payment_info = self.payment_details.get(payment_id)
        
        if payment_info is None:
            return None # Payment ID does not exist
        
        if payment_info['account_id'] != account_id:
            return None # Payment belongs to a different account
            
        return payment_info['status']