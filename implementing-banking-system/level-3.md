# Implementing Banking System 
**Level 1:** The banking system should support creating new accounts, deposting money into accounts, and transferring money between two accounts.

**Level 2:** The banking system should support ranking accounts based on outgoing transactions 

**Level 3:** The banking system should allow scheduling	 payments with cashback and checking the status of scheduled payments.

**Level 4:** The banking system should support merging two accounts while retaining both accounts	 balance and transaction histories


## Banking System - Level 3

This problem extends the banking system functionality to include scheduled payments with cashback and the ability to check the status of these scheduled payments.

### General Notes:

* All operations will have a `timestamp` parameter – a stringified timestamp in milliseconds. It is guaranteed that all timestamps are unique and are in a range from 1 to $10^9$. Operations will be given in order of strictly increasing timestamps.

## Methods to Implement:

1.  **`pay(self, timestamp: int, account_id: str, amount: int) -> str | None`:**
    * Withdraws the given `amount` of money from the specified `account_id`.
    * All withdrawal transactions provide a 2% cashback.
    * The 2% of the withdrawn amount (rounded down to the nearest integer) will be refunded to the account 24 hours after the withdrawal. If the withdrawal is successful (i.e., the account holds sufficient funds to withdraw the given amount), returns a string with a unique identifier for the payment transaction in this format: `"payment[ordinal number of withdrawals from all accounts]"` - e.g., `"payment1"`, `"payment2"`, etc.
    * The waiting period for cashback is 24 hours, equal to $24 \cdot 60 \cdot 60 \cdot 1000 = ********$ milliseconds (the unit for timestamps). So, cashback will be processed at `timestamp + ********`.
    * def top_spenders(self, timestamp, n) should also account for the total amount of money withdrawn from accounts due to the `pay` operation.
    * When it's time to process cashback for a withdrawal, the amount must be refunded to the account before any other transactions are performed at the relevant timestamp.
    * Returns `None` if `account_id` doesn't exist.
    * Returns `None` if `account_id` has insufficient funds to perform the payment.

2.  **`get_payment_status(self, timestamp: int, account_id: str, payment_str: str) -> str | None`:**
    * Returns the status of the payment transaction for the given `payment_str`.
    * Returns `None` if `account_id` doesn't exist.
    * Returns `None` if the given `payment_str` doesn't exist for the specified account.
    * Returns `None` if the payment transaction was for an account with a different identifier from `account_id`.
    * Returns a string representing the payment status: `"IN_PROGRESS"` or `"CASHBACK_RECEIVED"`.


## Examples:

The example below shows how these operations should work:

| Queries                                             | Explanations                                                                                                                                                                                                                                           |
| :-------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `create_account(1, "account1")`                     | returns `True`                                                                                                                                                                                                                                         |
| `create_account(2, "account2")`                     | returns `True`                                                                                                                                                                                                                                         |
| `deposit(3, "account1", 2000)`                      | returns `2000`                                                                                                                                                                                                                                         |
| `pay(4, "account1", 1000)`                          | returns `"payment1"`                                                                                                                                                                                                                                   |
| `pay(100, "account1", 1000)`                        | returns `"payment2"`                                                                                                                                                                                                                                   |
| `get_payment_status(101, "non-existing", "payment1")` | returns `None`; this account does not exist                                                                                                                                                                                                            |
| `get_payment_status(102, "account2", "payment1")`   | returns `None`; this payment was from another account                                                                                                                                                                                                  |
| `get_payment_status(103, "account1", "payment1")`   | returns `"IN_PROGRESS"`                                                                                                                                                                                                                              |
| `top_spenders(104, 2)`                              | returns `["account1(2000)", "account2(0)"]`                                                                                                                                                                                                            |
| `deposit(3 + MILLISECONDS_IN_1_DAY, "account1", 100)` | returns `100`; cashback for `"payment1"` was not refunded yet (because deposit occurred before the cashback timestamp for payment1)                                                                                                                             |
| `get_payment_status(4 + MILLISECONDS_IN_1_DAY, "account1", "payment1")` | returns `"CASHBACK_RECEIVED"`                                                                                                                                                                                                                          |
| `deposit(5 + MILLISECONDS_IN_1_DAY, "account1", 100)` | returns `220`; cashback of `20` from `"payment1"` was refunded (original balance was 100, then 100 deposited, then 20 cashback from payment1. So 100+100+20=220)                                                                                        |
| `deposit(99 + MILLISECONDS_IN_1_DAY, "account1", 100)` | returns `320`; cashback for `"payment2"` was not refunded yet (original balance was 220, then 100 deposited. So 220+100=320)                                                                                                                             |
| `deposit(100 + MILLISECONDS_IN_1_DAY, "account1", 100)` | returns `440`; cashback of `20` from `"payment2"` was refunded (original balance was 320, then 100 deposited, then 20 cashback from payment2. So 320+100+20=440) |

## Constraints:

* Execution time limit: 3 seconds
* Memory limit: 4g