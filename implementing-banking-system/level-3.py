from banking_system import BankingSystem

MILLISECONDS_IN_A_DAY = ********

class BankingSystemImpl(BankingSystem):
    def __init__(self):
        self.accounts = {}
        self.outgoing_transactions = {}
  
        self.payments = {}

    def create_account(self, timestamp, account_id):
        if account_id in self.accounts:
            return False
        self.accounts[account_id] = 0
        self.outgoing_transactions[account_id] = 0
        return True
    
    def deposit(self, timestamp, account_id, amount):
        if (account_id not in self.accounts) or amount <= 0:
            return None
        self.accounts[account_id] += amount
        return self.accounts[account_id] 
    
    def transfer(self, timestamp, source_account_id, target_account_id, amount):
        if source_account_id == target_account_id:
            return None
        if (source_account_id not in self.accounts) or (target_account_id not in self.accounts) or amount <= 0:
            return None
        if self.accounts[source_account_id] < amount:
            return None
        self.accounts[source_account_id] -= amount
        self.accounts[target_account_id] += amount

        self.outgoing_transactions[source_account_id] += amount

        return self.accounts[source_account_id]
    
    def top_spenders(self, timestamp, n):
        outgoing_totals = [(account_id, total) for account_id, total in \
                           self.outgoing_transactions.items()]
        outgoing_totals.sort(key=lambda x: (-x[1], x[0]))

        result = [
            f"{account_id}({total})" for account_id, total in outgoing_totals[:n]
        ]

        return result
    
    def pay(self, timestamp, account_id, amount):
        if (account_id not in self.accounts) or amount <= 0:
            return None
        if self.accounts[account_id] < amount:
            return None
        
        self.payments
        self.accounts[account_id] -= amount
        # add to outgoing transactions
        self.outgoing_transactions[account_id] += amount

 