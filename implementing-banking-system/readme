Level 1: The banking system should support creating new accounts, deposting money into accounts, and transferring money between two accounts.

Level2: The banking system should support ranking accounts based on outgoing transactions 

Level 3: The banking system should allow scheduling	 payments with cashback and checking the status of scheduled payments.

Level 4: The banking system should support merging two accounts while retaining both accounts	 balance and transaction histories